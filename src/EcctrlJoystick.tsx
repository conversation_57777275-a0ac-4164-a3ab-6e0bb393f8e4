import * as THREE from 'three';
import { Canvas, type ThreeElements } from '@react-three/fiber';
import React, { forwardRef, type ReactNode, Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { animated, useSpring } from '@react-spring/three';
import { useJoystickControls } from './stores/useJoystickControls';
import GlobalSpaceEvent, { GlobalDataKey } from '@/world/Global/GlobalSpaceEvent';

const JoystickComponents = (props: EcctrlJoystickProps) => {
  /**
   * Preset values/components
   */
  let joystickCenterX: number = 0;
  let joystickCenterY: number = 0;
  let joystickHalfWidth: number = 0;
  let joystickHalfHeight: number = 0;
  let joystickMaxDis: number = 0;
  let joystickDis: number = 0;
  let joystickAng: number = 0;
  const touch1MovementVec2 = useMemo(() => new THREE.Vector2(), []);
  const joystickMovementVec2 = useMemo(() => new THREE.Vector2(), []);

  const [windowSize, setWindowSize] = useState({ innerHeight, innerWidth });
  const joystickDivRef = useRef<HTMLDivElement>(null);
  const [buttonPos, setButtonPos] = useState({ x: 0, y: 0 });
  const [joystickState, setJoystickState] = useState({ dis: 0, ang: 0, maxDis: 0 });

  const [isMobileFlip, setIsMobileFlip] = useState(false);

  useEffect(() => {
    const unsubscribe = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.IsMobileFlip,
      (value) => {
        setIsMobileFlip(value);
      },
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.IsMobileFlip, unsubscribe);
    };
  }, []);
  /**
   * Joystick store setup
   */
  const setJoystick = useJoystickControls((state) => state.setJoystick);
  const resetJoystick = useJoystickControls((state) => state.resetJoystick);

  // Touch move function
  const onTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault();
    e.stopImmediatePropagation();
    const touch1 = e.targetTouches[0];

    const touch1MovementX = isMobileFlip ? touch1.pageY - joystickCenterY : touch1.pageX - joystickCenterX;
    const touch1MovementY = isMobileFlip ? touch1.pageX - joystickCenterX : -(touch1.pageY - joystickCenterY);
    touch1MovementVec2.set(touch1MovementX, touch1MovementY);

    joystickDis = Math.min(Math.sqrt(Math.pow(touch1MovementX, 2) + Math.pow(touch1MovementY, 2)), joystickMaxDis);
    joystickAng = touch1MovementVec2.angle();
    joystickMovementVec2.set(joystickDis * Math.cos(joystickAng), joystickDis * Math.sin(joystickAng));
    const runState = joystickDis > joystickMaxDis * (props.joystickRunSensitivity ?? 0.9);

    // Apply animations
    const maxMovement = joystickMaxDis * 0.65; // 最大移动距离
    const normalizedDis = Math.min(joystickDis / joystickMaxDis, 1); // 标准化距离
    const buttonMoveX = Math.cos(joystickAng) * normalizedDis * maxMovement;
    const buttonMoveY = -Math.sin(joystickAng) * normalizedDis * maxMovement;

    setButtonPos({
      x: buttonMoveX,
      y: buttonMoveY,
    });

    // 更新摇杆状态供箭头使用
    setJoystickState({
      dis: joystickDis,
      ang: joystickAng,
      maxDis: joystickMaxDis,
    });

    // Pass valus to joystick store
    setJoystick(joystickDis, joystickAng, runState);
  }, [windowSize, isMobileFlip]);

  // Touch end function
  const onTouchEnd = (e: TouchEvent) => {
    setButtonPos({
      x: 0,
      y: 0,
    });
    // 重置摇杆状态
    setJoystickState({
      dis: 0,
      ang: 0,
      maxDis: joystickMaxDis,
    });
    // Reset joystick store values
    resetJoystick();
  };

  // Reset window size function
  const onWindowResize = () => {
    setWindowSize({ innerHeight: window.innerHeight, innerWidth: window.innerWidth });
  };

  useEffect(() => {
    if (!joystickDivRef.current) return;
    const joystickPositionX = joystickDivRef.current.getBoundingClientRect().x;
    const joystickPositionY = joystickDivRef.current.getBoundingClientRect().y;
    joystickHalfWidth = joystickDivRef.current.getBoundingClientRect().width / 2;
    joystickHalfHeight = joystickDivRef.current.getBoundingClientRect().height / 2;

    joystickMaxDis = joystickHalfWidth;

    joystickCenterX = joystickPositionX + joystickHalfWidth;
    joystickCenterY = joystickPositionY + joystickHalfHeight;

    joystickDivRef.current.addEventListener('touchmove', onTouchMove, { passive: false });
    joystickDivRef.current.addEventListener('touchend', onTouchEnd);

    window.visualViewport.addEventListener('resize', onWindowResize);

    return () => {
      if (joystickDivRef.current) {
        joystickDivRef.current.removeEventListener('touchmove', onTouchMove);
        joystickDivRef.current.removeEventListener('touchend', onTouchEnd);
      }
      window.visualViewport.removeEventListener('resize', onWindowResize);
    };
  });

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }} ref={joystickDivRef}>
      {/* 摇杆背景 */}
      <img
        src="/image/joystick/stick_bg.png"
        style={{
          width: '100%',
          height: '100%',
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1,
        }}
        alt="joystick background"
      />

      {/* 摇杆按钮 - 使用 springs 动画值 */}
      <img
        src="/image/joystick/stick_button.png"
        style={{
          width: '40%',
          height: '40%',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: `translate(-50%, -50%) translate(${buttonPos.x}px, ${buttonPos.y}px)`,
          zIndex: 2,
        }}
        alt="joystick button"
      />

      {/* 摇杆方向箭头 - 显示在外边缘 */}
      {/*{joystickState.dis > 0 && (*/}
      <div
        style={{
          top: '50%',
          left: '50%',
          position: 'absolute',
          // 将箭头放置在背景圆的外边缘
          transform: `rotate(${-joystickState.ang + Math.PI / 2}rad)`,
          zIndex: 3,
          transition: 'opacity 0.1s ease-out',
        }}>
        <img
          src="/image/joystick/stick_arrow.png"
          style={{
            width: '2.8rem',
            height: '1rem',
            position: 'absolute',
            top: '-7.2rem',
            // 将箭头放置在背景圆的外边缘
            transform: `translate(-50%, -50%)`,
            zIndex: 3,
            opacity: joystickState.maxDis > 0 ? Math.min(joystickState.dis / joystickState.maxDis, 1) : 0,
            transition: 'opacity 0.1s ease-out',
          }}
          alt="joystick direction"
        />
      </div>

      {/*)}*/}
    </div>
  );
};

const ButtonComponents = ({ buttonNumber = 1, ...props }: EcctrlJoystickProps) => {
  /**
   * Button component geometries
   */
  const buttonLargeBaseGeo = useMemo(() => new THREE.CylinderGeometry(1.1, 1, 0.3, 16), []);
  const buttonSmallBaseGeo = useMemo(() => new THREE.CylinderGeometry(0.9, 0.8, 0.3, 16), []);
  const buttonTop1Geo = useMemo(() => new THREE.CylinderGeometry(0.9, 0.9, 0.5, 16), []);
  const buttonTop2Geo = useMemo(() => new THREE.CylinderGeometry(0.9, 0.9, 0.5, 16), []);
  const buttonTop3Geo = useMemo(() => new THREE.CylinderGeometry(0.7, 0.7, 0.5, 16), []);
  const buttonTop4Geo = useMemo(() => new THREE.CylinderGeometry(0.7, 0.7, 0.5, 16), []);
  const buttonTop5Geo = useMemo(() => new THREE.CylinderGeometry(0.7, 0.7, 0.5, 16), []);

  /**
   * Button component materials
   */
  const buttonBaseMaterial = useMemo(() => new THREE.MeshNormalMaterial({ transparent: true, opacity: 0.3 }), []);
  const buttonTop1Material = useMemo(() => new THREE.MeshNormalMaterial({ transparent: true, opacity: 0.5 }), []);
  const buttonTop2Material = useMemo(() => new THREE.MeshNormalMaterial({ transparent: true, opacity: 0.5 }), []);
  const buttonTop3Material = useMemo(() => new THREE.MeshNormalMaterial({ transparent: true, opacity: 0.5 }), []);
  const buttonTop4Material = useMemo(() => new THREE.MeshNormalMaterial({ transparent: true, opacity: 0.5 }), []);
  const buttonTop5Material = useMemo(() => new THREE.MeshNormalMaterial({ transparent: true, opacity: 0.5 }), []);

  const buttonDiv: HTMLDivElement = document.querySelector('#ecctrl-button');

  /**
   * Animation preset
   */
  const [springs, api] = useSpring(
    () => ({
      buttonTop1BaseScaleY: 1,
      buttonTop1BaseScaleXAndZ: 1,
      buttonTop2BaseScaleY: 1,
      buttonTop2BaseScaleXAndZ: 1,
      buttonTop3BaseScaleY: 1,
      buttonTop3BaseScaleXAndZ: 1,
      buttonTop4BaseScaleY: 1,
      buttonTop4BaseScaleXAndZ: 1,
      buttonTop5BaseScaleY: 1,
      buttonTop5BaseScaleXAndZ: 1,
      config: {
        tension: 600,
      },
    }),
  );

  /**
   * Button store setup
   */
  const pressButton1 = useJoystickControls((state) => state.pressButton1);
  const pressButton2 = useJoystickControls((state) => state.pressButton2);
  const pressButton3 = useJoystickControls((state) => state.pressButton3);
  const pressButton4 = useJoystickControls((state) => state.pressButton4);
  const pressButton5 = useJoystickControls((state) => state.pressButton5);
  const releaseAllButtons = useJoystickControls((state) => state.releaseAllButtons);

  // Pointer down function
  const onPointerDown = (number: number) => {
    switch (number) {
      case 1:
        pressButton1();
        api.start({
          buttonTop1BaseScaleY: 0.5,
          buttonTop1BaseScaleXAndZ: 1.15,
        });
        break;
      case 2:
        pressButton2();
        api.start({
          buttonTop2BaseScaleY: 0.5,
          buttonTop2BaseScaleXAndZ: 1.15,
        });
        break;
      case 3:
        pressButton3();
        api.start({
          buttonTop3BaseScaleY: 0.5,
          buttonTop3BaseScaleXAndZ: 1.15,
        });
        break;
      case 4:
        pressButton4();
        api.start({
          buttonTop4BaseScaleY: 0.5,
          buttonTop4BaseScaleXAndZ: 1.15,
        });
        break;
      case 5:
        pressButton5();
        api.start({
          buttonTop5BaseScaleY: 0.5,
          buttonTop5BaseScaleXAndZ: 1.15,
        });
        break;
      default:
        break;
    }
  };

  // Pointer up function
  const onPointerUp = () => {
    releaseAllButtons();
    api.start({
      buttonTop1BaseScaleY: 1,
      buttonTop1BaseScaleXAndZ: 1,
      buttonTop2BaseScaleY: 1,
      buttonTop2BaseScaleXAndZ: 1,
      buttonTop3BaseScaleY: 1,
      buttonTop3BaseScaleXAndZ: 1,
      buttonTop4BaseScaleY: 1,
      buttonTop4BaseScaleXAndZ: 1,
      buttonTop5BaseScaleY: 1,
      buttonTop5BaseScaleXAndZ: 1,
    });
  };

  useEffect(() => {
    buttonDiv.addEventListener('pointerup', onPointerUp);

    return () => {
      buttonDiv.removeEventListener('pointerup', onPointerUp);
    };
  });

  return (
    <Suspense fallback="null">
      {/* Button 1 */}
      {buttonNumber > 0 &&
        <animated.group
          scale-x={springs.buttonTop1BaseScaleXAndZ}
          scale-y={springs.buttonTop1BaseScaleY}
          scale-z={springs.buttonTop1BaseScaleXAndZ}
          rotation={[-Math.PI / 2, 0, 0]}
          position={props.buttonGroup1Position || (buttonNumber === 1 ? [0, 0, 0] : [2, 1, 0])}>
          <mesh geometry={buttonLargeBaseGeo} material={buttonBaseMaterial} {...props.buttonLargeBaseProps}
                onPointerDown={() => onPointerDown(1)} />
          <mesh geometry={buttonTop1Geo} material={buttonTop1Material}
                position={[0, -0.3, 0]} {...props.buttonTop1Props} />
        </animated.group>}
      {/* Button 2 */}
      {buttonNumber > 1 &&
        <animated.group
          scale-x={springs.buttonTop2BaseScaleXAndZ}
          scale-y={springs.buttonTop2BaseScaleY}
          scale-z={springs.buttonTop2BaseScaleXAndZ}
          rotation={[-Math.PI / 2, 0, 0]}
          position={props.buttonGroup2Position || [0.5, -1.3, 0]}>
          <mesh geometry={buttonLargeBaseGeo} material={buttonBaseMaterial} {...props.buttonLargeBaseProps}
                onPointerDown={() => onPointerDown(2)} />
          <mesh geometry={buttonTop2Geo} material={buttonTop2Material}
                position={[0, -0.3, 0]} {...props.buttonTop2Props} />
        </animated.group>}
      {/* Button 3 */}
      {buttonNumber > 2 &&
        <animated.group
          scale-x={springs.buttonTop3BaseScaleXAndZ}
          scale-y={springs.buttonTop3BaseScaleY}
          scale-z={springs.buttonTop3BaseScaleXAndZ}
          rotation={[-Math.PI / 2, 0, 0]}
          position={props.buttonGroup3Position || [-1, 1, 0]}>
          <mesh geometry={buttonSmallBaseGeo} material={buttonBaseMaterial} {...props.buttonSmallBaseProps}
                onPointerDown={() => onPointerDown(3)} />
          <mesh geometry={buttonTop3Geo} material={buttonTop3Material}
                position={[0, -0.3, 0]} {...props.buttonTop3Props} />
        </animated.group>}
      {/* Button 4 */}
      {buttonNumber > 3 &&
        <animated.group
          scale-x={springs.buttonTop4BaseScaleXAndZ}
          scale-y={springs.buttonTop4BaseScaleY}
          scale-z={springs.buttonTop4BaseScaleXAndZ}
          rotation={[-Math.PI / 2, 0, 0]}
          position={props.buttonGroup4Position || [-2, -1.3, 0]}>
          <mesh geometry={buttonSmallBaseGeo} material={buttonBaseMaterial} {...props.buttonSmallBaseProps}
                onPointerDown={() => onPointerDown(4)} />
          <mesh geometry={buttonTop4Geo} material={buttonTop4Material}
                position={[0, -0.3, 0]} {...props.buttonTop4Props} />
        </animated.group>}
      {/* Button 5 */}
      {buttonNumber > 4 &&
        <animated.group
          scale-x={springs.buttonTop5BaseScaleXAndZ}
          scale-y={springs.buttonTop5BaseScaleY}
          scale-z={springs.buttonTop5BaseScaleXAndZ}
          rotation={[-Math.PI / 2, 0, 0]}
          position={props.buttonGroup5Position || [0.4, 2.9, 0]}>
          <mesh geometry={buttonSmallBaseGeo} material={buttonBaseMaterial} {...props.buttonSmallBaseProps}
                onPointerDown={() => onPointerDown(5)} />
          <mesh geometry={buttonTop5Geo} material={buttonTop5Material}
                position={[0, -0.3, 0]} {...props.buttonTop5Props} />
        </animated.group>}
    </Suspense>
  );
};

export const EcctrlJoystick = forwardRef<HTMLDivElement, EcctrlJoystickProps>((props, ref) => {
  const joystickWrapperStyle: React.CSSProperties = {
    userSelect: 'none',
    MozUserSelect: 'none',
    WebkitUserSelect: 'none',
    msUserSelect: 'none',
    touchAction: 'none',
    // pointerEvents: 'none', // 移除鼠标事件拦截，允许直接点击
    overscrollBehavior: 'none',
    position: 'fixed',
    zIndex: '9999',
    height: props.joystickHeightAndWidth || '12.5rem',
    width: props.joystickHeightAndWidth || '12.5rem',
    left: props.joystickPositionLeft || '10rem',
    bottom: props.joystickPositionBottom || '6rem',
  };

  const buttonWrapperStyle: React.CSSProperties = {
    userSelect: 'none',
    MozUserSelect: 'none',
    WebkitUserSelect: 'none',
    msUserSelect: 'none',
    touchAction: 'none',
    pointerEvents: 'none',
    overscrollBehavior: 'none',
    position: 'fixed',
    zIndex: '9999',
    height: props.buttonHeightAndWidth || '200px',
    width: props.buttonHeightAndWidth || '200px',
    right: props.buttonPositionRight || '8rem',
    bottom: props.buttonPositionBottom || '4rem',
  };

  return (
    <div ref={ref}>
      <div id="ecctrl-joystick" style={joystickWrapperStyle} onContextMenu={(e) => e.preventDefault()}>
        <JoystickComponents {...props} />
      </div>
      {
        props.buttonNumber !== 0 &&
        <div id="ecctrl-button" style={buttonWrapperStyle} onContextMenu={(e) => e.preventDefault()}>
          <Canvas
            shadows
            orthographic
            camera={{
              zoom: props.buttonCamZoom || 26,
              position: props.buttonCamPosition || [0, 0, 50],
            }}>
            <ButtonComponents {...props} />
            {props.children}
          </Canvas>
        </div>
      }
    </div>
  );
});

export type EcctrlJoystickProps = {
  // Joystick props
  children?: ReactNode;
  joystickRunSensitivity?: number;
  joystickPositionLeft?: number;
  joystickPositionBottom?: number;
  joystickHeightAndWidth?: number;
  joystickCamZoom?: number;
  joystickCamPosition?: [x: number, y: number, z: number];
  joystickBaseProps?: ThreeElements['mesh'];
  joystickStickProps?: ThreeElements['mesh'];
  joystickHandleProps?: ThreeElements['mesh'];

  // Touch buttons props
  buttonNumber?: number;
  buttonPositionRight?: number;
  buttonPositionBottom?: number;
  buttonHeightAndWidth?: number;
  buttonCamZoom?: number;
  buttonCamPosition?: [x: number, y: number, z: number];
  buttonGroup1Position?: [x: number, y: number, z: number];
  buttonGroup2Position?: [x: number, y: number, z: number];
  buttonGroup3Position?: [x: number, y: number, z: number];
  buttonGroup4Position?: [x: number, y: number, z: number];
  buttonGroup5Position?: [x: number, y: number, z: number];
  buttonLargeBaseProps?: ThreeElements['mesh'];
  buttonSmallBaseProps?: ThreeElements['mesh'];
  buttonTop1Props?: ThreeElements['mesh'];
  buttonTop2Props?: ThreeElements['mesh'];
  buttonTop3Props?: ThreeElements['mesh'];
  buttonTop4Props?: ThreeElements['mesh'];
  buttonTop5Props?: ThreeElements['mesh'];
};