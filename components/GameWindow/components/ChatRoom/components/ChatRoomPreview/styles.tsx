import SvgWrapper from '@/components/SvgWrapper';
import styled, { css } from 'styled-components';

export const StyleButtonBox = styled.div`
  width: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 1.5rem;
  position: relative;
  height: 100%;
`;

export const PreviewButtonSvg = styled(SvgWrapper)<{ $withNew?: boolean }>`
  width: 3rem;
  height: 3rem;
  ${({ $withNew = false }) =>
    $withNew &&
    css`
      position: relative;
      &::after {
        content: '';
        position: absolute;
        display: block;
        width: 1rem;
        height: 1rem;
        box-sizing: border-box;
        top: 0;
        right: 0;
        border: 0.0625rem solid #fff;
        background: #ff4516;
        border-radius: 50%;
        transform: translate(50%, -25%);
      }
    `}
`;

export const PreviewButton = styled.button`
  width: 3rem;
  background: transparent;
  box-shadow: none;
  outline: inherit;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  & > span.text {
    color: #fff;
    text-shadow: 0rem 0rem 0.125rem rgba(0, 0, 0, 0.3);
    font-family: Inter;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
`;

export const PreviewMessageBox = styled.div`
  display: flex;
  padding: 0.5rem 0rem;
  align-items: flex-start;
  gap: 1.5rem;
  flex-direction: column;
  height: 100%;
  overflow: scroll;
  flex: 1;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border-radius: 1.5rem;
  padding: 1rem 0.5rem;
  background: transparent;
  transition: background 0.1s ease-in;
  width: 31.75rem;
  &:hover {
    background: rgba(0, 0, 0, 0.6);
  }
`;
